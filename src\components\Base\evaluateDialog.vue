<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits, computed } from "vue";
import { requestTo } from "@/utils/http/tool";
import { pinyin } from "pinyin-pro";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { useRouter } from "vue-router";
import baseUrl, { codeInfo } from "@/utils/http/base.js";
const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: String,
    default: ""
  },
  id: {
    type: Number
  },
  name: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  account: {
    type: String,
    default: ""
  },
  dialogFormVisible: {
    type: Boolean
  },
  logOut: {
    type: Boolean,
    default: true
  },
  operateLogType: {
    type: String,
    default: "COMPLEX_MANAGEMENT"
  },
  textLeftBtn: {
    type: String,
    default: "取消"
  },
  textRightBtn: {
    type: String,
    default: "确认"
  },
  operateType: {
    type: String,
    default: "重置了密码"
  },
  showContent: {
    type: String,
    default: "resetPassword"
  },
  marginLeft: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["reset", "update:dialogFormVisible", "updateData"]);
const router = useRouter();
const reason = ref("");
onMounted(() => {
  console.log(props.title);
  generatePassword();
});
// const dialogFormVisible = ref(false);
const newPassword = ref("");
const getListLoading = ref(false);
const btnOKClick = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: props.id,
    password: encryption(newPassword.value)
  };
  if (props.showContent === "groupOrder") {
    // console.log(
    //   "🌳-----import.meta.env.VITE_APP_SERVER_ID-----",
    //   import.meta.env.VITE_APP_SERVER_ID
    // );

    // console.log('🌳baseUrl------------------------------>', baseUrl);
    paramsData = {
      coursePeriodId: props.id,
      qrCode: {
        scene: props.id,
        page: "pages/home/<USER>",
        ...codeInfo.qrParams
      }
    };
  }
  if (props.showContent === "examine") {
    paramsData = {
      ids: props.id,
      reason: reason.value,
      auditState: "REJECTED"
    };
  }
  const operateLog = {
    operateLogType: props.operateLogType,
    operateType: props.operateType || "重置了密码"
    // operatorTarget: form.value.name,
  };
  // console.log("🐳paramsData------------------------------>", paramsData);
  // return
  try {
    const { code, data, msg } = await props.api(paramsData, operateLog);
    if (code === 200) {
      if (props.showContent === "groupOrder") {
        router.push({
          path: "/course/currentDetails/groupOrder",
          query: { periodId: props.id, ordersId: data.ordersId || "" }
        });
      } else if (props.showContent === "examine") {
        ElMessage({
          message: "审核成功",
          type: "success"
        });
        selectedReasons.value = [];
        reason.value = "";
        emit("updateData");
      } else {
        ElMessage({
          message: "重置密码成功",
          type: "success"
        });
        /** 退出登录 */
        if (props.logOut) {
          useUserStoreHook().logOut();
        }
      }
    } else {
      if (props.showContent === "groupOrder") {
        if (code === 30039) {
          ElMessage.error(`课程定制失败，${msg}，请去编辑`);
        } else if (code === 30040) {
          ElMessage.error(`课程定制失败，${msg}，请去检查`);
        } else if (code === 30034) {
          ElMessage.error(`课程定制失败，开课时间已过期`);
        } else {
          ElMessage.error(`课程定制失败，${msg}`);
        }
      } else if (props.showContent === "examine") {
        ElMessage.error(`审核失败，${msg}`);
      }
    }
  } catch (error) {
    if (props.showContent === "groupOrder") {
      ElMessage.error(error);
    } else if (props.showContent === "examine") {
      ElMessage.error(`审核失败，${msg}`);
    } else {
      console.error("生成密码失败：", error);
    }
  }
  getListLoading.value = false;
  emit("reset");
};
// 生成管理员账号的方法
const generatePassword = () => {
  try {
    // 1. 获取姓名首字母
    const initials = pinyin(props.name, {
      pattern: "first", // 只保留首字母
      toneType: "none", // 不显示声调
      type: "array" // 返回数组格式
    })
      .join("")
      .toLowerCase();

    // 2. 获取手机号后6位
    const phonePart = props.phone.slice(-6);

    // 3. 组合生成密码
    newPassword.value = `${initials}${phonePart}@`;
    console.log("🌈-----newPassword.value-----", newPassword.value);
  } catch (error) {
    console.error("生成密码失败：", error);
    // password.value = '生成失败，请检查输入';
  }
};
const cancel = () => {
  selectedReasons.value = [];
  reason.value = "";
  emit("reset");
};
// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value); // 通知父组件更新
  }
});
const selectReason = ref();
const selectedReasons = ref([]); // 用于跟踪已选择的理由
const reasonList = ref([
  { id: 0, name: "不适当言论" },
  { id: 1, name: "敏感信息" },
  { id: 2, name: "违规言论" }
]);
const selectReasonEvt = (item, index) => {
  const isSelected = selectedReasons.value.some(
    selected => selected.id === item.id
  );

  if (isSelected) {
    // 如果已选择，则移除
    const index = selectedReasons.value.findIndex(
      selected => selected.id === item.id
    );
    selectedReasons.value.splice(index, 1);
  } else {
    // 如果未选择，则添加
    selectedReasons.value.push(item);
  }

  // 更新文本框内容为所有已选择理由的连接
  reason.value = selectedReasons.value
    .map(reasonItem => reasonItem.name)
    .join("，");
};
const handleReasonInput = value => {
  reason.value = value;
  // 可以在这里添加更复杂的同步逻辑，但为简化，我们只更新值
};
const handleClose = () => {
  selectedReasons.value = [];
  reason.value = "";
};
</script>

<template>
  <!-- <div class="popup"> -->
  <el-dialog
    v-model="localVisible"
    :title="title || '重置密码确定'"
    width="515"
    @close="handleClose"
  >
    <div class="content">
      <div v-if="showContent === 'resetPassword'" class="info">
        <p>重置密码的账号 {{ props.account }}</p>
        <p>密码将被重置为 {{ newPassword }}</p>
        <p>密码生成规则：姓名首字母+手机号后6位+@</p>
      </div>
      <div v-if="showContent === 'groupOrder'" class="describe">
        <p>说明:</p>
        <p>1、一旦开启课程定制，该课程将无法在平台上进行公开展示，</p>
        <p>只能通过分享链接的方式让特定人群进行购买。</p>
        <p>
          2、一旦开启课程定制，该课程的所有信息将无法被修改。如需修改请取消定制后再进行修改。
        </p>
        <p>3、如果一旦有用户付款下单且没有退款，则无法取消定制。</p>
      </div>
      <div v-if="showContent === 'examine'" class="des-examine">
        <div>
          <el-input
            v-model="reason"
            placeholder="请选择或输入理由"
            type="textarea"
            resize="none"
            maxlength="100"
            show-word-limit
            @input="handleReasonInput"
          />
          <div class="select-btn">
            <div
              v-for="(item, index) in reasonList"
              :key="index"
              :class="
                selectedReasons.some(selected => selected.id === item.id)
                  ? 'active-item'
                  : 'select-item'
              "
              @click="selectReasonEvt(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button :style="{ 'margin-right': marginLeft }" @click="cancel">
          {{ textLeftBtn }}
        </el-button>
        <el-button
          :loading="getListLoading"
          :type="
            textRightBtn === '确认'
              ? 'primary'
              : textRightBtn === '确认开启'
                ? 'primary'
                : 'danger'
          "
          @click="btnOKClick"
        >
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  :nth-child(2) {
    margin: 20px 0 20px 0px;
  }
}
.describe {
  width: 80%;
  height: 130px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
.des-examine {
  width: 96%;
  height: 200px;
  margin: 0 auto;
  :deep(.el-textarea__inner) {
    height: 150px;
  }
  .select-btn {
    margin-top: 10px;
    display: flex;
    .select-item {
      padding: 5px;
      border: 1px solid #ccc;
      margin-right: 10px;
      border-radius: 5px;
      cursor: pointer;
      &:hover {
        color: #409eff;
        border-color: #dae8f6;
        background-color: #ebf5ff;
      }
    }
    .active-item {
      padding: 5px;
      //   border: 1px solid #409eff;
      background-color: #409eff;
      color: #fff;
      margin-right: 10px;
      border-radius: 5px;
    }
  }
}
</style>
