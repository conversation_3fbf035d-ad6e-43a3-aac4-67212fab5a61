/**
 * 接口域名的管理
 * @param {String} apiServer [api服务器]
 * @param {String} staffTemplate    // 专家库导入模板
 */
const baseLsit = [
  // 本地开发环境 0
  {
    // apiServer: "https://test.wktest.cn:3001/api",
    // apiServer: "http://10.0.30.109:9000",
    // apiServer: "http://10.0.30.183:9000", //qiu
    // apiServer: "http://10.0.30.117:9000", //ren
    // apiServer: "http://10.0.30.194:3001/api",
    // apiServer: "https://gs-svc.gostaredu.com"
    // knobbleServer: "http://10.0.30.117/section"
    apiServer: "http://k8s-dev.boran-tech.cn:31392",
    filePreviewServer: "https://api.ebag.readboy.com/wps",
    staffTemplate: "https://s.gostaredu.boran-tech.com", // 专家库导入模板
    imageProxy: "http://k8s-dev.boran-tech.cn:32185" // 图片代理
  },
  // 本地K8S环境 1
  {
    apiServer: "http://k8s-dev.boran-tech.cn:31392",
    filePreviewServer: "https://api.ebag.readboy.com/wps",
    // knobbleServer: "http://10.0.30.109/section",
    staffTemplate: "https://s.gostaredu.boran-tech.com", // 专家库导入模板
    imageProxy: "http://k8s-dev.boran-tech.cn:32185" // 图片代理
  },
  // 正式环境 2
  {
    apiServer: "https://gs-svc.gostaredu.com",
    filePreviewServer: "https://api.ebag.readboy.com/wps",
    staffTemplate: "https://s.gostaredu.boran-tech.com", // 专家库导入模板
    imageProxy: "https://gs-svc.gostaredu.com/img-cmp/" // 图片代理
    // knobbleServer: "http://10.0.30.109/section"
  }
];

/**
 * //微信登录不同环境code码
 * @param {String} code [code码]
 */
const codeList = [
  // 本地开发环境 0
  {
    url: "https://gs-svc.gostaredu.com",
    code: 1,
    app_id: "wx4f152d3ccf5af3ed"
  },
  // 本地K8S环境 1
  {
    url: "https://gs-svc.gostaredu.com",
    code: 1,
    app_id: "wx4f152d3ccf5af3ed"
  },
  //正式环境 2
  {
    url: "https://gs-svc.gostaredu.com",
    code: 3,
    app_id: "wx4f152d3ccf5af3ed"
  },
  //中山环境 3
  { url: "", code: 5, app_id: "wx4f152d3ccf5af3ed" }
];
/**
 * 数据分析驾驶舱url地址
 *
 */
const urlList = [
  // 本地开发环境 0
  {
    userImageUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/70fcbc8811f082af5f91c33488efc4c2.png", //用户画像驾驶舱地址
    institutionUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/10bb12fcd25ac7fdc32e3d509a4aa34d.png", //机构运营驾驶舱地址
    platformUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/8acd3855ca215492aaa8c63c184daed4.jpg", //平台运营驾驶舱地址
    courseUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/90cad022292cf9da826ab4b878b10ab6.png", //课程运营驾驶舱地址
    activeUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/e6a6fd1341859dcd6eab10c1622f6fd6.png" //用户行为分析驾驶舱地址
  },
  // 本地K8S环境 1
  {
    userImageUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/70fcbc8811f082af5f91c33488efc4c2.png", //用户画像驾驶舱地址
    institutionUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/10bb12fcd25ac7fdc32e3d509a4aa34d.png", //机构运营驾驶舱地址
    platformUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/8acd3855ca215492aaa8c63c184daed4.jpg", //平台运营驾驶舱地址
    courseUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/90cad022292cf9da826ab4b878b10ab6.png", //课程运营驾驶舱地址
    activeUrl:
      "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/e6a6fd1341859dcd6eab10c1622f6fd6.png" //用户行为分析驾驶舱地址
  },
  //正式环境 2
  {
    userImageUrl:
      "https://gs-svc.gostaredu.com/research-learning/prod/70fcbc8811f082af5f91c33488efc4c2.png", //用户画像驾驶舱地址
    institutionUrl:
      "https://gs-svc.gostaredu.com/research-learning/prod/10bb12fcd25ac7fdc32e3d509a4aa34d.png", //机构运营驾驶舱地址
    platformUrl:
      "https://gs-svc.gostaredu.com/research-learning/prod/8acd3855ca215492aaa8c63c184daed4.jpg", //平台运营驾驶舱地址
    courseUrl:
      "https://gs-svc.gostaredu.com/research-learning/prod/90cad022292cf9da826ab4b878b10ab6.png", //课程运营驾驶舱地址
    activeUrl:
      "https://gs-svc.gostaredu.com/research-learning/prod/e6a6fd1341859dcd6eab10c1622f6fd6.png" //用户行为分析驾驶舱地址
  },
  // 中山环境
  {
    userImageUrl:
      "https://ziyuan.ebag.readboy.com/research-learning/70fcbc8811f082af5f91c33488efc4c2.png", //用户画像驾驶舱地址
    institutionUrl:
      "https://ziyuan.ebag.readboy.com/research-learning/10bb12fcd25ac7fdc32e3d509a4aa34d.png", //机构运营驾驶舱地址
    platformUrl:
      "https://ziyuan.ebag.readboy.com/research-learning/8acd3855ca215492aaa8c63c184daed4.jpg", //平台运营驾驶舱地址
    courseUrl:
      "https://ziyuan.ebag.readboy.com/research-learning/90cad022292cf9da826ab4b878b10ab6.png", //课程运营驾驶舱地址
    activeUrl:
      "https://ziyuan.ebag.readboy.com/research-learning/e6a6fd1341859dcd6eab10c1622f6fd6.png" //用户行为分析驾驶舱地址
  }
];
const ServerNumber = import.meta.env.VITE_APP_SERVER_ID
  ? import.meta.env.VITE_APP_SERVER_ID
  : 0;
// const ServerNumber = 1;
const baseUrl = baseLsit[ServerNumber];
export const codeInfo = codeList[ServerNumber];
export const urlInfo = urlList[ServerNumber];

export default baseUrl;
