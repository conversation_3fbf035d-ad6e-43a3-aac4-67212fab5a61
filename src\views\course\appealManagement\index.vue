<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

defineOptions({
  name: "AppealManagementIndex"
});

// 申诉状态tab选项
const activeTab = ref(0);
const tabOptions = ref([
  { id: 0, name: "待处理", value: "PENDING" },
  { id: 1, name: "已处理", value: "PROCESSED" }
]);

// 申诉数据对象
const appealObj = ref({
  tabId: 0,
  state: "PENDING",
  showBatchAudit: true // 待处理显示批量审核，已处理不显示
});

// 切换tab
const handleTabClick = (item, index) => {
  activeTab.value = item.props.name;

  if (activeTab.value === 0) {
    // 待处理
    appealObj.value.tabId = 0;
    appealObj.value.state = "PENDING";
    appealObj.value.showBatchAudit = true;
    // TODO: 调用获取待处理申诉列表的方法
    console.log("切换到待处理tab");
  } else {
    // 已处理
    appealObj.value.tabId = 1;
    appealObj.value.state = "PROCESSED";
    appealObj.value.showBatchAudit = false;
    // TODO: 调用获取已处理申诉列表的方法
    console.log("切换到已处理tab");
  }
};
</script>

<template>
  <div class="containers">
    <div class="main">
      <!-- 申诉管理tab栏 -->
      <div class="appeal-tabs">
        <el-tabs
          v-model="activeTab"
          class="demo-tabs"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="(item, index) in tabOptions"
            :key="index"
            :label="item.name"
            :name="index"
          />
        </el-tabs>
      </div>

      <!-- 申诉列表内容区域 -->
      <div class="appeal-content">
        <!-- TODO: 这里将放置申诉列表表格组件 -->
        <div class="placeholder-content">
          <p>当前选中: {{ tabOptions[activeTab].name }}</p>
          <p>状态: {{ appealObj.state }}</p>
          <p>是否显示批量审核: {{ appealObj.showBatchAudit ? '是' : '否' }}</p>
          <p style="color: #999; margin-top: 20px;">
            申诉列表表格组件将在后续添加...
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  display: flex;
  flex-direction: column;
  height: 88vh;
  overflow: hidden;
  box-sizing: border-box;

  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
  }
}

// 申诉管理tab样式
.appeal-tabs {
  margin-bottom: 20px;

  :deep(.el-tabs) {
    --el-tabs-header-height: 40px;
  }

  // 保持蓝色下划线效果
  :deep(.el-tabs__active-bar) {
    background-color: #409eff;
    height: 2px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background-color: #e4e7ed;
    height: 1px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    font-weight: 500;
    color: #606266;

    &.is-active {
      color: #409eff;
      font-weight: 600;
    }

    &:hover {
      color: #409eff;
    }
  }
}

// 申诉内容区域
.appeal-content {
  flex: 1;
  overflow: hidden;

  .placeholder-content {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;
    border: 1px dashed #dcdfe6;
    text-align: center;

    p {
      margin: 8px 0;
      font-size: 14px;
      color: #606266;

      &:first-child {
        font-weight: 600;
        color: #303133;
      }
    }
  }
}
</style>
